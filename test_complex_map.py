"""
复杂地图测试脚本
包含大量多边形和随机点障碍物的高难度测试
"""

import numpy as np
import random
import time
from voronoi_planner import VoronoiPlanner
from obstacles import PolygonObstacle, CircleObstacle, PointObstacle
from visualizer import VoronoiVisualizer


def create_ultra_complex_map():
    """创建超复杂地图：大量多边形 + 密集随机点"""
    print("创建超复杂地图...")
    
    planner = VoronoiPlanner(map_size=(50, 50), safety_margin=0.7)
    
    # === 第一部分：各种复杂多边形障碍物 ===
    
    # 1. 大型不规则多边形群
    irregular_polygons = [
        # 不规则八边形
        [(8, 8), (12, 6), (16, 8), (18, 12), (16, 16), (12, 18), (8, 16), (6, 12)],
        # 复杂凹多边形
        [(25, 8), (35, 8), (35, 12), (30, 12), (30, 16), (35, 16), (35, 20), (25, 20), (25, 16), (28, 16), (28, 12), (25, 12)],
        # 星形多边形
        [(42, 10), (44, 6), (46, 10), (50, 8), (47, 12), (48, 16), (44, 14), (40, 16), (41, 12), (38, 8)],
    ]
    
    for vertices in irregular_polygons:
        polygon = PolygonObstacle(vertices)
        planner.add_obstacle(polygon)
    
    # 2. L形和T形障碍物群
    l_t_shapes = [
        # L形1
        [(5, 25), (15, 25), (15, 30), (10, 30), (10, 35), (5, 35)],
        # T形1
        [(20, 25), (35, 25), (35, 30), (30, 30), (30, 40), (25, 40), (25, 30), (20, 30)],
        # 反L形
        [(40, 25), (45, 25), (45, 40), (40, 40), (40, 35), (42, 35), (42, 30), (40, 30)],
    ]
    
    for vertices in l_t_shapes:
        polygon = PolygonObstacle(vertices)
        planner.add_obstacle(polygon)
    
    # 3. 几何形状组合
    geometric_shapes = []
    
    # 多个六边形
    for center in [(15, 42), (35, 42)]:
        hex_points = []
        for i in range(6):
            angle = i * np.pi / 3
            x = center[0] + 3 * np.cos(angle)
            y = center[1] + 3 * np.sin(angle)
            hex_points.append((x, y))
        geometric_shapes.append(hex_points)
    
    # 多个五边形
    for center in [(8, 35), (42, 35)]:
        pent_points = []
        for i in range(5):
            angle = i * 2 * np.pi / 5
            x = center[0] + 2.5 * np.cos(angle)
            y = center[1] + 2.5 * np.sin(angle)
            pent_points.append((x, y))
        geometric_shapes.append(pent_points)
    
    for vertices in geometric_shapes:
        polygon = PolygonObstacle(vertices)
        planner.add_obstacle(polygon)
    
    # 4. 添加一些圆形障碍物
    circles = [
        CircleObstacle((12, 20), 1.5),
        CircleObstacle((38, 20), 2),
        CircleObstacle((22, 35), 1.8),
        CircleObstacle((28, 15), 1.2),
        CircleObstacle((45, 45), 2.5),
        CircleObstacle((5, 45), 1.8),
    ]
    
    for circle in circles:
        planner.add_obstacle(circle)
    
    print(f"已添加 {len(planner.obstacles)} 个几何障碍物")
    
    # === 第二部分：大量随机点障碍物 ===
    
    random.seed(789)  # 固定随机种子
    num_random_points = 100  # 大量随机点
    
    attempts = 0
    points_added = 0
    max_attempts = 500
    
    print("开始添加随机点障碍物...")
    
    while points_added < num_random_points and attempts < max_attempts:
        x = random.uniform(1, 49)
        y = random.uniform(1, 49)
        
        # 检查点是否与现有障碍物冲突
        point_valid = True
        min_distance_to_obstacles = float('inf')
        
        for obs in planner.obstacles:
            if obs.is_point_inside((x, y), margin=2.0):
                point_valid = False
                break
        
        if point_valid:
            # 随机影响半径，根据周围障碍物密度调整
            base_radius = random.uniform(0.4, 1.2)
            
            # 检查周围障碍物密度
            nearby_obstacles = 0
            for obs in planner.obstacles:
                if isinstance(obs, PointObstacle):
                    distance = np.sqrt((x - obs.position[0])**2 + (y - obs.position[1])**2)
                    if distance < 5:
                        nearby_obstacles += 1
            
            # 如果周围障碍物较多，减小影响半径
            if nearby_obstacles > 3:
                influence_radius = base_radius * 0.7
            else:
                influence_radius = base_radius
            
            point_obs = PointObstacle((x, y), influence_radius=influence_radius)
            planner.add_obstacle(point_obs)
            points_added += 1
            
            if points_added % 20 == 0:
                print(f"已添加 {points_added} 个随机点...")
        
        attempts += 1
    
    print(f"成功添加了 {points_added} 个随机点障碍物")
    print(f"总障碍物数量: {len(planner.obstacles)}")
    
    return planner


def test_multiple_paths(planner):
    """测试多条不同的路径"""
    test_cases = [
        ((2, 2), (48, 48), "对角线路径"),
        ((2, 48), (48, 2), "反对角线路径"),
        ((25, 2), (25, 48), "垂直路径"),
        ((2, 25), (48, 25), "水平路径"),
        ((10, 10), (40, 40), "中等距离路径"),
        ((5, 20), (45, 30), "复杂路径1"),
        ((15, 5), (35, 45), "复杂路径2"),
    ]
    
    results = []
    
    print("\n开始多路径测试...")
    print("=" * 50)
    
    for i, (start, goal, description) in enumerate(test_cases):
        print(f"\n测试 {i+1}: {description}")
        print(f"起点: {start}, 终点: {goal}")
        
        start_time = time.time()
        path = planner.find_path_with_retry(start, goal, max_retries=3)
        planning_time = time.time() - start_time
        
        if path:
            # 计算路径统计
            stats = planner.get_path_statistics(path)
            print(f"✅ 路径规划成功!")
            print(f"   规划时间: {planning_time:.3f}秒")
            print(f"   路径长度: {stats['total_distance']:.2f}")
            print(f"   路径点数: {stats['num_waypoints']}")
            print(f"   平均段长: {stats['average_segment_length']:.2f}")
            
            results.append({
                'description': description,
                'success': True,
                'time': planning_time,
                'stats': stats
            })
        else:
            print(f"❌ 路径规划失败!")
            results.append({
                'description': description,
                'success': False,
                'time': planning_time,
                'stats': None
            })
    
    return results


def main():
    """主测试函数"""
    print("超复杂地图Voronoi路径规划测试")
    print("=" * 60)
    
    # 创建复杂地图
    planner = create_ultra_complex_map()
    
    # 生成Voronoi图
    print("\n生成Voronoi图...")
    start_time = time.time()
    success = planner.generate_voronoi_diagram()
    voronoi_time = time.time() - start_time
    
    if not success:
        print("❌ Voronoi图生成失败!")
        return
    
    print(f"✅ Voronoi图生成成功!")
    print(f"   生成时间: {voronoi_time:.3f}秒")
    print(f"   Voronoi顶点数: {len(planner.voronoi.vertices)}")
    print(f"   图节点数: {len(planner.voronoi_graph)}")
    
    # 测试多条路径
    results = test_multiple_paths(planner)
    
    # 可视化最具挑战性的路径
    print("\n生成可视化...")
    visualizer = VoronoiVisualizer(planner, figsize=(15, 12))
    
    # 选择一条成功的路径进行可视化
    successful_paths = [r for r in results if r['success']]
    if successful_paths:
        # 选择最长的路径进行展示
        longest_path_result = max(successful_paths, 
                                key=lambda x: x['stats']['total_distance'])
        
        # 重新规划这条路径用于可视化
        test_cases = [
            ((2, 2), (48, 48), "对角线路径"),
            ((2, 48), (48, 2), "反对角线路径"),
            ((25, 2), (25, 48), "垂直路径"),
            ((2, 25), (48, 25), "水平路径"),
            ((10, 10), (40, 40), "中等距离路径"),
            ((5, 20), (45, 30), "复杂路径1"),
            ((15, 5), (35, 45), "复杂路径2"),
        ]
        
        # 找到对应的起点和终点
        for start, goal, desc in test_cases:
            if desc == longest_path_result['description']:
                path = planner.find_path(start, goal)
                visualizer.show_complete_visualization(start, goal, path)
                break
    else:
        # 如果没有成功的路径，只显示地图和Voronoi图
        visualizer.show_complete_visualization()
    
    # 保存结果
    visualizer.save_figure("ultra_complex_map_result.png", dpi=300)
    visualizer.show()
    
    # 打印总结
    print("\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    
    successful_count = sum(1 for r in results if r['success'])
    total_count = len(results)
    
    print(f"总测试数: {total_count}")
    print(f"成功数: {successful_count}")
    print(f"成功率: {successful_count/total_count*100:.1f}%")
    
    if successful_paths:
        avg_time = np.mean([r['time'] for r in successful_paths])
        avg_distance = np.mean([r['stats']['total_distance'] for r in successful_paths])
        print(f"平均规划时间: {avg_time:.3f}秒")
        print(f"平均路径长度: {avg_distance:.2f}")


if __name__ == "__main__":
    main()
