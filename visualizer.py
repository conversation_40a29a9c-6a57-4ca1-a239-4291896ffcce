"""
Voronoi路径规划可视化模块
提供交互式可视化功能
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Polygon, Circle
import numpy as np
from typing import List, Tuple, Optional
from obstacles import Obstacle, PolygonObstacle, CircleObstacle, PointObstacle
from voronoi_planner import VoronoiPlanner


class VoronoiVisualizer:
    """Voronoi路径规划可视化器"""
    
    def __init__(self, planner: VoronoiPlanner, figsize: Tuple[int, int] = (12, 10)):
        """
        初始化可视化器
        
        Args:
            planner: Voronoi路径规划器
            figsize: 图形尺寸
        """
        self.planner = planner
        self.fig, self.ax = plt.subplots(figsize=figsize)
        self.setup_plot()
        
    def setup_plot(self):
        """设置绘图环境"""
        self.ax.set_xlim(0, self.planner.map_width)
        self.ax.set_ylim(0, self.planner.map_height)
        self.ax.set_aspect('equal')
        self.ax.grid(True, alpha=0.3)
        self.ax.set_xlabel('X坐标')
        self.ax.set_ylabel('Y坐标')
        self.ax.set_title('基于Voronoi图的路径规划')
        
    def clear_plot(self):
        """清除绘图内容"""
        self.ax.clear()
        self.setup_plot()
        
    def draw_obstacles(self, alpha: float = 0.7):
        """绘制障碍物"""
        for obstacle in self.planner.obstacles:
            if isinstance(obstacle, PolygonObstacle):
                self._draw_polygon_obstacle(obstacle, alpha)
            elif isinstance(obstacle, CircleObstacle):
                self._draw_circle_obstacle(obstacle, alpha)
            elif isinstance(obstacle, PointObstacle):
                self._draw_point_obstacle(obstacle, alpha)
                
    def _draw_polygon_obstacle(self, obstacle: PolygonObstacle, alpha: float):
        """绘制多边形障碍物"""
        polygon = Polygon(obstacle.vertices, closed=True, 
                         facecolor='red', edgecolor='darkred', 
                         alpha=alpha, linewidth=2)
        self.ax.add_patch(polygon)
        
        # 标注顶点
        for i, vertex in enumerate(obstacle.vertices):
            self.ax.plot(vertex[0], vertex[1], 'ro', markersize=4)
            
    def _draw_circle_obstacle(self, obstacle: CircleObstacle, alpha: float):
        """绘制圆形障碍物"""
        circle = Circle(obstacle.center, obstacle.radius, 
                       facecolor='red', edgecolor='darkred', 
                       alpha=alpha, linewidth=2)
        self.ax.add_patch(circle)
        
        # 标注圆心
        self.ax.plot(obstacle.center[0], obstacle.center[1], 'ro', markersize=6)
        
    def _draw_point_obstacle(self, obstacle: PointObstacle, alpha: float):
        """绘制点障碍物"""
        # 绘制影响范围
        circle = Circle(obstacle.position, obstacle.influence_radius, 
                       facecolor='red', edgecolor='darkred', 
                       alpha=alpha*0.5, linewidth=1, linestyle='--')
        self.ax.add_patch(circle)
        
        # 绘制点
        self.ax.plot(obstacle.position[0], obstacle.position[1], 'ro', markersize=8)
        
    def draw_voronoi_diagram(self, show_points: bool = True, show_edges: bool = True):
        """绘制Voronoi图"""
        if self.planner.voronoi is None:
            print("警告：Voronoi图尚未生成")
            return
            
        voronoi = self.planner.voronoi
        
        # 绘制Voronoi点（种子点）
        if show_points:
            self.ax.plot(voronoi.points[:, 0], voronoi.points[:, 1], 
                        'bo', markersize=2, alpha=0.6, label='Voronoi种子点')
            
        # 绘制Voronoi边
        if show_edges:
            self._draw_voronoi_edges()
            
        # 绘制Voronoi顶点
        if len(voronoi.vertices) > 0:
            valid_vertices = []
            for vertex in voronoi.vertices:
                if (0 <= vertex[0] <= self.planner.map_width and 
                    0 <= vertex[1] <= self.planner.map_height):
                    valid_vertices.append(vertex)
                    
            if valid_vertices:
                valid_vertices = np.array(valid_vertices)
                self.ax.plot(valid_vertices[:, 0], valid_vertices[:, 1], 
                           'go', markersize=3, alpha=0.8, label='Voronoi顶点')
                
    def _draw_voronoi_edges(self):
        """绘制Voronoi边"""
        voronoi = self.planner.voronoi
        
        for ridge_vertices in voronoi.ridge_vertices:
            if -1 not in ridge_vertices:  # 排除无限边
                v1, v2 = ridge_vertices
                vertex1 = voronoi.vertices[v1]
                vertex2 = voronoi.vertices[v2]
                
                # 检查边是否在地图范围内
                if (self._is_vertex_in_bounds(vertex1) and 
                    self._is_vertex_in_bounds(vertex2)):
                    
                    # 检查边是否有效（不与障碍物碰撞）
                    if self.planner._is_edge_valid(tuple(vertex1), tuple(vertex2)):
                        self.ax.plot([vertex1[0], vertex2[0]], 
                                   [vertex1[1], vertex2[1]], 
                                   'g-', linewidth=1, alpha=0.6)
                    else:
                        # 用虚线绘制无效边
                        self.ax.plot([vertex1[0], vertex2[0]], 
                                   [vertex1[1], vertex2[1]], 
                                   'g--', linewidth=0.5, alpha=0.3)
                                   
    def _is_vertex_in_bounds(self, vertex) -> bool:
        """检查顶点是否在地图范围内"""
        return (0 <= vertex[0] <= self.planner.map_width and 
                0 <= vertex[1] <= self.planner.map_height)
                
    def draw_path(self, path: List[Tuple[float, float]], 
                  color: str = 'blue', linewidth: float = 3, 
                  show_waypoints: bool = True):
        """绘制路径"""
        if not path or len(path) < 2:
            return
            
        # 绘制路径线
        x_coords = [point[0] for point in path]
        y_coords = [point[1] for point in path]
        
        self.ax.plot(x_coords, y_coords, color=color, linewidth=linewidth, 
                    alpha=0.8, label='规划路径')
        
        # 绘制路径点
        if show_waypoints:
            self.ax.plot(x_coords, y_coords, 'o', color=color, 
                        markersize=6, alpha=0.8)
            
        # 标注起始点和目标点
        if len(path) >= 2:
            self.ax.plot(path[0][0], path[0][1], 's', color='green', 
                        markersize=10, label='起始点')
            self.ax.plot(path[-1][0], path[-1][1], '^', color='red', 
                        markersize=10, label='目标点')
                        
    def draw_safety_margin(self):
        """绘制安全边距可视化"""
        for obstacle in self.planner.obstacles:
            if isinstance(obstacle, CircleObstacle):
                # 为圆形障碍物绘制安全边距
                safety_circle = Circle(obstacle.center, 
                                     obstacle.radius + self.planner.safety_margin,
                                     facecolor='none', edgecolor='orange', 
                                     alpha=0.5, linewidth=1, linestyle=':')
                self.ax.add_patch(safety_circle)
                
    def show_complete_visualization(self, start: Optional[Tuple[float, float]] = None,
                                  goal: Optional[Tuple[float, float]] = None,
                                  path: Optional[List[Tuple[float, float]]] = None):
        """显示完整的可视化"""
        self.clear_plot()
        
        # 绘制障碍物
        self.draw_obstacles()
        
        # 绘制安全边距
        self.draw_safety_margin()
        
        # 绘制Voronoi图
        self.draw_voronoi_diagram()
        
        # 绘制路径
        if path:
            self.draw_path(path)
        elif start and goal:
            # 如果没有提供路径但有起始点和目标点，尝试规划路径
            planned_path = self.planner.find_path(start, goal)
            if planned_path:
                self.draw_path(planned_path)
            else:
                print("无法找到有效路径")
                # 仍然标注起始点和目标点
                self.ax.plot(start[0], start[1], 's', color='green', 
                           markersize=10, label='起始点')
                self.ax.plot(goal[0], goal[1], '^', color='red', 
                           markersize=10, label='目标点')
                           
        # 添加图例
        self.ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        # 调整布局
        plt.tight_layout()
        
    def save_figure(self, filename: str, dpi: int = 300):
        """保存图形"""
        self.fig.savefig(filename, dpi=dpi, bbox_inches='tight')
        print(f"图形已保存为: {filename}")
        
    def show(self):
        """显示图形"""
        plt.show()
        
    def close(self):
        """关闭图形"""
        plt.close(self.fig)
