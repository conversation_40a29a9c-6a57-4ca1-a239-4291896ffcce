"""
Voronoi路径规划演示脚本
在50x50地图上测试各种障碍物配置
"""

import numpy as np
import random
import time
from voronoi_planner import VoronoiPlanner
from obstacles import PolygonObstacle, CircleObstacle, PointObstacle
from visualizer import VoronoiVisualizer


def create_demo_scenario_1():
    """演示场景1：基本多边形和圆形障碍物"""
    print("=== 演示场景1：基本几何障碍物 ===")
    
    # 创建规划器
    planner = VoronoiPlanner(map_size=(50, 50), safety_margin=1.5)
    
    # 添加多边形障碍物
    polygon1 = PolygonObstacle([
        (10, 10), (20, 10), (20, 20), (10, 20)
    ])
    planner.add_obstacle(polygon1)
    
    # 添加三角形障碍物
    triangle = PolygonObstacle([
        (30, 15), (40, 10), (40, 25)
    ])
    planner.add_obstacle(triangle)
    
    # 添加圆形障碍物
    circle1 = CircleObstacle(center=(25, 35), radius=5)
    planner.add_obstacle(circle1)
    
    circle2 = CircleObstacle(center=(15, 35), radius=3)
    planner.add_obstacle(circle2)
    
    return planner


def create_demo_scenario_2():
    """演示场景2：复杂多边形和点障碍物"""
    print("=== 演示场景2：复杂障碍物组合 ===")
    
    planner = VoronoiPlanner(map_size=(50, 50), safety_margin=1.0)
    
    # 添加L形障碍物
    l_shape = PolygonObstacle([
        (5, 5), (15, 5), (15, 15), (25, 15), 
        (25, 25), (5, 25)
    ])
    planner.add_obstacle(l_shape)
    
    # 添加星形障碍物
    star_points = []
    center_x, center_y = 35, 35
    outer_radius = 6
    inner_radius = 3
    
    for i in range(10):
        angle = i * np.pi / 5
        if i % 2 == 0:
            radius = outer_radius
        else:
            radius = inner_radius
        x = center_x + radius * np.cos(angle)
        y = center_y + radius * np.sin(angle)
        star_points.append((x, y))
    
    star = PolygonObstacle(star_points)
    planner.add_obstacle(star)
    
    # 添加随机点障碍物
    random.seed(42)
    for _ in range(8):
        x = random.uniform(5, 45)
        y = random.uniform(5, 45)
        # 确保点不在已有障碍物内
        if not any(obs.is_point_inside((x, y), margin=2) for obs in planner.obstacles):
            point_obs = PointObstacle((x, y), influence_radius=1.5)
            planner.add_obstacle(point_obs)
    
    return planner


def create_demo_scenario_3():
    """演示场景3：迷宫式障碍物"""
    print("=== 演示场景3：迷宫式环境 ===")
    
    planner = VoronoiPlanner(map_size=(50, 50), safety_margin=0.8)
    
    # 创建迷宫式障碍物
    # 垂直墙壁
    for x in [12, 25, 38]:
        for y_start in [5, 20, 35]:
            wall = PolygonObstacle([
                (x, y_start), (x+2, y_start), 
                (x+2, y_start+10), (x, y_start+10)
            ])
            planner.add_obstacle(wall)
    
    # 水平墙壁
    for y in [15, 30]:
        for x_start in [8, 22, 35]:
            wall = PolygonObstacle([
                (x_start, y), (x_start+8, y), 
                (x_start+8, y+2), (x_start, y+2)
            ])
            planner.add_obstacle(wall)
    
    # 添加一些圆形障碍物作为额外挑战
    circles = [
        CircleObstacle((18, 8), 2),
        CircleObstacle((32, 22), 2.5),
        CircleObstacle((42, 42), 3)
    ]
    
    for circle in circles:
        planner.add_obstacle(circle)
    
    return planner


def run_path_planning_demo(planner, start, goal, scenario_name):
    """运行路径规划演示"""
    print(f"\n开始{scenario_name}路径规划...")
    print(f"起始点: {start}")
    print(f"目标点: {goal}")
    
    # 生成Voronoi图
    print("生成Voronoi图...")
    start_time = time.time()
    success = planner.generate_voronoi_diagram()
    voronoi_time = time.time() - start_time
    
    if not success:
        print("Voronoi图生成失败！")
        return None, None
    
    print(f"Voronoi图生成完成，耗时: {voronoi_time:.3f}秒")
    print(f"Voronoi顶点数量: {len(planner.voronoi.vertices) if planner.voronoi else 0}")
    print(f"Voronoi图节点数量: {len(planner.voronoi_graph)}")
    
    # 路径规划
    print("开始路径搜索...")
    start_time = time.time()
    path = planner.find_path(start, goal)
    planning_time = time.time() - start_time
    
    if path:
        print(f"路径规划成功！耗时: {planning_time:.3f}秒")
        print(f"路径长度: {len(path)}个点")
        
        # 计算路径总长度
        total_distance = 0
        for i in range(len(path) - 1):
            dx = path[i+1][0] - path[i][0]
            dy = path[i+1][1] - path[i][1]
            total_distance += np.sqrt(dx*dx + dy*dy)
        print(f"路径总距离: {total_distance:.2f}")
    else:
        print("路径规划失败！")
    
    return path, (voronoi_time, planning_time)


def main():
    """主演示函数"""
    print("基于Voronoi图的路径规划算法演示")
    print("=" * 50)
    
    # 演示场景列表
    scenarios = [
        (create_demo_scenario_1, (5, 5), (45, 45), "场景1"),
        (create_demo_scenario_2, (2, 2), (48, 48), "场景2"),
        (create_demo_scenario_3, (3, 3), (47, 47), "场景3")
    ]
    
    all_results = []
    
    for scenario_func, start, goal, name in scenarios:
        print(f"\n{'='*20} {name} {'='*20}")
        
        # 创建场景
        planner = scenario_func()
        
        # 运行路径规划
        path, timing = run_path_planning_demo(planner, start, goal, name)
        
        # 可视化
        print("生成可视化...")
        visualizer = VoronoiVisualizer(planner)
        visualizer.show_complete_visualization(start, goal, path)
        
        # 保存结果图
        filename = f"voronoi_demo_{name.lower().replace('场景', 'scenario_')}.png"
        visualizer.save_figure(filename)
        
        # 记录结果
        result = {
            'scenario': name,
            'success': path is not None,
            'path_length': len(path) if path else 0,
            'timing': timing
        }
        all_results.append(result)
        
        # 显示图形
        visualizer.show()
        visualizer.close()
    
    # 打印总结
    print("\n" + "="*50)
    print("演示总结:")
    print("="*50)
    
    for result in all_results:
        print(f"{result['scenario']}:")
        print(f"  规划成功: {'是' if result['success'] else '否'}")
        if result['success']:
            print(f"  路径点数: {result['path_length']}")
            if result['timing']:
                print(f"  Voronoi生成时间: {result['timing'][0]:.3f}秒")
                print(f"  路径搜索时间: {result['timing'][1]:.3f}秒")
        print()


if __name__ == "__main__":
    main()
