"""
障碍物类定义
支持多边形、圆形和点障碍物
"""

import numpy as np
import math
from typing import List, <PERSON>ple
from abc import ABC, abstractmethod


class Obstacle(ABC):
    """障碍物基类"""
    
    @abstractmethod
    def get_boundary_points(self, num_points: int = None) -> List[Tuple[float, float]]:
        """获取障碍物边界点"""
        pass
        
    @abstractmethod
    def is_point_inside(self, point: Tuple[float, float], margin: float = 0.0) -> bool:
        """检查点是否在障碍物内部（考虑安全边距）"""
        pass
        
    @abstractmethod
    def get_center(self) -> Tuple[float, float]:
        """获取障碍物中心点"""
        pass


class PolygonObstacle(Obstacle):
    """多边形障碍物"""
    
    def __init__(self, vertices: List[Tuple[float, float]]):
        """
        初始化多边形障碍物
        
        Args:
            vertices: 多边形顶点列表，按逆时针顺序
        """
        self.vertices = vertices
        
    def get_boundary_points(self, num_points: int = None) -> List[Tuple[float, float]]:
        """获取多边形边界上的采样点"""
        if num_points is None:
            # 根据多边形周长自动确定采样点数量
            perimeter = self._calculate_perimeter()
            num_points = max(10, int(perimeter * 2))
            
        boundary_points = []
        total_length = self._calculate_perimeter()
        
        if total_length == 0:
            return self.vertices.copy()
            
        current_length = 0
        target_spacing = total_length / num_points
        
        for i in range(len(self.vertices)):
            v1 = self.vertices[i]
            v2 = self.vertices[(i + 1) % len(self.vertices)]
            
            edge_length = math.sqrt((v2[0] - v1[0])**2 + (v2[1] - v1[1])**2)
            
            # 在这条边上采样点
            while current_length < total_length:
                if current_length >= (len(boundary_points)) * target_spacing:
                    # 计算在当前边上的位置
                    edge_progress = (current_length - sum(self._get_edge_lengths()[:i])) / edge_length
                    edge_progress = max(0, min(1, edge_progress))
                    
                    x = v1[0] + edge_progress * (v2[0] - v1[0])
                    y = v1[1] + edge_progress * (v2[1] - v1[1])
                    boundary_points.append((x, y))
                    
                    if len(boundary_points) >= num_points:
                        break
                        
                current_length += target_spacing
                
            if len(boundary_points) >= num_points:
                break
                
        return boundary_points[:num_points]
        
    def _calculate_perimeter(self) -> float:
        """计算多边形周长"""
        perimeter = 0
        for i in range(len(self.vertices)):
            v1 = self.vertices[i]
            v2 = self.vertices[(i + 1) % len(self.vertices)]
            perimeter += math.sqrt((v2[0] - v1[0])**2 + (v2[1] - v1[1])**2)
        return perimeter
        
    def _get_edge_lengths(self) -> List[float]:
        """获取所有边的长度"""
        lengths = []
        for i in range(len(self.vertices)):
            v1 = self.vertices[i]
            v2 = self.vertices[(i + 1) % len(self.vertices)]
            length = math.sqrt((v2[0] - v1[0])**2 + (v2[1] - v1[1])**2)
            lengths.append(length)
        return lengths
        
    def is_point_inside(self, point: Tuple[float, float], margin: float = 0.0) -> bool:
        """使用射线法检查点是否在多边形内部"""
        x, y = point
        
        # 如果有安全边距，先检查是否在扩展的多边形内
        if margin > 0:
            # 简化处理：检查点到多边形边界的最小距离
            min_distance = self._point_to_polygon_distance(point)
            if min_distance <= margin:
                return True
                
        # 标准的点在多边形内判断（射线法）
        n = len(self.vertices)
        inside = False
        
        p1x, p1y = self.vertices[0]
        for i in range(1, n + 1):
            p2x, p2y = self.vertices[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
            
        return inside
        
    def _point_to_polygon_distance(self, point: Tuple[float, float]) -> float:
        """计算点到多边形边界的最小距离"""
        min_distance = float('inf')
        x, y = point
        
        for i in range(len(self.vertices)):
            v1 = self.vertices[i]
            v2 = self.vertices[(i + 1) % len(self.vertices)]
            
            # 计算点到线段的距离
            distance = self._point_to_line_segment_distance(point, v1, v2)
            min_distance = min(min_distance, distance)
            
        return min_distance
        
    def _point_to_line_segment_distance(self, point: Tuple[float, float], 
                                      line_start: Tuple[float, float], 
                                      line_end: Tuple[float, float]) -> float:
        """计算点到线段的最小距离"""
        x, y = point
        x1, y1 = line_start
        x2, y2 = line_end
        
        # 线段长度的平方
        line_length_sq = (x2 - x1)**2 + (y2 - y1)**2
        
        if line_length_sq == 0:
            # 线段退化为点
            return math.sqrt((x - x1)**2 + (y - y1)**2)
            
        # 计算投影参数
        t = max(0, min(1, ((x - x1) * (x2 - x1) + (y - y1) * (y2 - y1)) / line_length_sq))
        
        # 投影点
        proj_x = x1 + t * (x2 - x1)
        proj_y = y1 + t * (y2 - y1)
        
        # 返回距离
        return math.sqrt((x - proj_x)**2 + (y - proj_y)**2)
        
    def get_center(self) -> Tuple[float, float]:
        """获取多边形的重心"""
        if not self.vertices:
            return (0, 0)
            
        x_sum = sum(v[0] for v in self.vertices)
        y_sum = sum(v[1] for v in self.vertices)
        
        return (x_sum / len(self.vertices), y_sum / len(self.vertices))


class CircleObstacle(Obstacle):
    """圆形障碍物"""
    
    def __init__(self, center: Tuple[float, float], radius: float):
        """
        初始化圆形障碍物
        
        Args:
            center: 圆心坐标
            radius: 半径
        """
        self.center = center
        self.radius = radius
        
    def get_boundary_points(self, num_points: int = None) -> List[Tuple[float, float]]:
        """获取圆周上的采样点"""
        if num_points is None:
            # 根据圆的周长自动确定采样点数量
            circumference = 2 * math.pi * self.radius
            num_points = max(8, int(circumference))
            
        boundary_points = []
        cx, cy = self.center
        
        for i in range(num_points):
            angle = 2 * math.pi * i / num_points
            x = cx + self.radius * math.cos(angle)
            y = cy + self.radius * math.sin(angle)
            boundary_points.append((x, y))
            
        return boundary_points
        
    def is_point_inside(self, point: Tuple[float, float], margin: float = 0.0) -> bool:
        """检查点是否在圆内（考虑安全边距）"""
        distance = math.sqrt((point[0] - self.center[0])**2 + (point[1] - self.center[1])**2)
        return distance <= (self.radius + margin)
        
    def get_center(self) -> Tuple[float, float]:
        """获取圆心"""
        return self.center


class PointObstacle(Obstacle):
    """点障碍物"""
    
    def __init__(self, position: Tuple[float, float], influence_radius: float = 1.0):
        """
        初始化点障碍物
        
        Args:
            position: 点的位置
            influence_radius: 影响半径
        """
        self.position = position
        self.influence_radius = influence_radius
        
    def get_boundary_points(self, num_points: int = None) -> List[Tuple[float, float]]:
        """点障碍物返回其位置"""
        return [self.position]
        
    def is_point_inside(self, point: Tuple[float, float], margin: float = 0.0) -> bool:
        """检查点是否在影响范围内"""
        distance = math.sqrt((point[0] - self.position[0])**2 + (point[1] - self.position[1])**2)
        return distance <= (self.influence_radius + margin)
        
    def get_center(self) -> Tuple[float, float]:
        """获取点的位置"""
        return self.position
