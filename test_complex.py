"""
复杂场景测试脚本
包含大量多边形、圆形和随机点障碍物的复杂地图测试
"""

import numpy as np
import random
import math
from voronoi_planner import VoronoiPlanner
from obstacles import PolygonObstacle, CircleObstacle, PointObstacle
from visualizer import VoronoiVisualizer


def create_complex_polygon_obstacles(planner):
    """创建复杂的多边形障碍物"""

    # 1. L形障碍物
    l_shape1 = PolygonObstacle([(8, 8), (18, 8), (18, 15), (25, 15), (25, 25), (8, 25)])
    planner.add_obstacle(l_shape1)

    # 2. 另一个L形障碍物（旋转）
    l_shape2 = PolygonObstacle(
        [(30, 35), (40, 35), (40, 42), (47, 42), (47, 47), (30, 47)]
    )
    planner.add_obstacle(l_shape2)

    # 3. T形障碍物
    t_shape = PolygonObstacle(
        [(35, 8), (45, 8), (45, 12), (42, 12), (42, 22), (38, 22), (38, 12), (35, 12)]
    )
    planner.add_obstacle(t_shape)

    # 4. 六边形障碍物
    hex_center = (15, 35)
    hex_radius = 4
    hex_points = []
    for i in range(6):
        angle = i * math.pi / 3
        x = hex_center[0] + hex_radius * math.cos(angle)
        y = hex_center[1] + hex_radius * math.sin(angle)
        hex_points.append((x, y))
    hexagon = PolygonObstacle(hex_points)
    planner.add_obstacle(hexagon)

    # 5. 星形障碍物
    star_center = (35, 25)
    star_points = []
    outer_radius = 5
    inner_radius = 2.5
    for i in range(10):
        angle = i * math.pi / 5
        if i % 2 == 0:
            radius = outer_radius
        else:
            radius = inner_radius
        x = star_center[0] + radius * math.cos(angle)
        y = star_center[1] + radius * math.sin(angle)
        star_points.append((x, y))
    star = PolygonObstacle(star_points)
    planner.add_obstacle(star)

    # 6. 不规则多边形（模拟建筑物）
    building1 = PolygonObstacle(
        [(5, 30), (12, 30), (12, 32), (15, 32), (15, 38), (10, 38), (10, 40), (5, 40)]
    )
    planner.add_obstacle(building1)

    # 7. 梯形障碍物
    trapezoid = PolygonObstacle([(25, 30), (35, 30), (32, 35), (28, 35)])
    planner.add_obstacle(trapezoid)

    # 8. 复杂凹多边形
    complex_shape = PolygonObstacle(
        [
            (42, 25),
            (48, 25),
            (48, 30),
            (45, 30),
            (45, 32),
            (48, 32),
            (48, 35),
            (42, 35),
            (42, 32),
            (44, 32),
            (44, 30),
            (42, 30),
        ]
    )
    planner.add_obstacle(complex_shape)

    # 9. 螺旋形多边形
    spiral_points = []
    center_x, center_y = 20, 40
    for i in range(12):
        angle = i * math.pi / 6
        radius = 2 + i * 0.3
        x = center_x + radius * math.cos(angle)
        y = center_y + radius * math.sin(angle)
        spiral_points.append((x, y))
    spiral = PolygonObstacle(spiral_points)
    planner.add_obstacle(spiral)

    # 10. 锯齿形障碍物
    zigzag_points = []
    for i in range(8):
        x = 5 + i * 2
        y = 15 + (2 if i % 2 == 0 else -2)
        zigzag_points.append((x, y))
    zigzag = PolygonObstacle(zigzag_points)
    planner.add_obstacle(zigzag)

    # 11. 十字形障碍物
    cross = PolygonObstacle(
        [
            (22, 5),
            (28, 5),
            (28, 10),
            (33, 10),
            (33, 16),
            (28, 16),
            (28, 21),
            (22, 21),
            (22, 16),
            (17, 16),
            (17, 10),
            (22, 10),
        ]
    )
    planner.add_obstacle(cross)

    # 12. 箭头形障碍物
    arrow = PolygonObstacle(
        [(40, 5), (45, 8), (43, 8), (43, 12), (37, 12), (37, 8), (35, 8)]
    )
    planner.add_obstacle(arrow)


def create_circle_obstacles(planner):
    """创建圆形障碍物"""
    circles = [
        CircleObstacle((12, 15), 2.5),
        CircleObstacle((28, 12), 3),
        CircleObstacle((20, 20), 1.8),
        CircleObstacle((40, 15), 2.2),
        CircleObstacle((8, 42), 2),
        CircleObstacle((25, 42), 1.5),
        CircleObstacle((42, 40), 2.8),
        CircleObstacle((18, 45), 1.2),
    ]

    for circle in circles:
        planner.add_obstacle(circle)


def create_random_point_obstacles(planner, num_points=50):
    """创建大量随机点障碍物"""
    random.seed(42)  # 固定随机种子以便重现

    points_added = 0
    max_attempts = num_points * 5  # 增加尝试次数
    attempts = 0

    # 分区域添加点，确保分布更均匀
    regions = [
        (3, 15, 3, 15),  # 左下
        (15, 35, 3, 15),  # 中下
        (35, 47, 3, 15),  # 右下
        (3, 15, 15, 35),  # 左中
        (15, 35, 15, 35),  # 中中
        (35, 47, 15, 35),  # 右中
        (3, 15, 35, 47),  # 左上
        (15, 35, 35, 47),  # 中上
        (35, 47, 35, 47),  # 右上
    ]

    points_per_region = num_points // len(regions)

    for region in regions:
        x_min, x_max, y_min, y_max = region
        region_points = 0

        while region_points < points_per_region and attempts < max_attempts:
            attempts += 1

            # 在当前区域随机生成点的位置
            x = random.uniform(x_min, x_max)
            y = random.uniform(y_min, y_max)

            # 随机生成影响半径（更大的变化范围）
            influence_radius = random.uniform(0.5, 3.0)

            # 检查是否与现有障碍物冲突
            point_valid = True
            test_point = (x, y)

            for obstacle in planner.obstacles:
                # 检查是否与现有障碍物重叠（减小间距要求）
                if obstacle.is_point_inside(test_point, margin=influence_radius + 0.5):
                    point_valid = False
                    break

            if point_valid:
                point_obstacle = PointObstacle(test_point, influence_radius)
                planner.add_obstacle(point_obstacle)
                points_added += 1
                region_points += 1

    # 添加剩余的随机点
    remaining_points = num_points - points_added
    while remaining_points > 0 and attempts < max_attempts:
        attempts += 1

        x = random.uniform(3, 47)
        y = random.uniform(3, 47)
        influence_radius = random.uniform(0.5, 2.0)

        point_valid = True
        test_point = (x, y)

        for obstacle in planner.obstacles:
            if obstacle.is_point_inside(test_point, margin=influence_radius + 0.3):
                point_valid = False
                break

        if point_valid:
            point_obstacle = PointObstacle(test_point, influence_radius)
            planner.add_obstacle(point_obstacle)
            points_added += 1
            remaining_points -= 1

    print(f"成功添加了 {points_added} 个随机点障碍物")


def create_maze_like_obstacles(planner):
    """创建迷宫式障碍物"""

    # 创建一些墙壁
    walls = [
        # 垂直墙壁
        PolygonObstacle([(10, 5), (11, 5), (11, 12), (10, 12)]),
        PolygonObstacle([(20, 8), (21, 8), (21, 18), (20, 18)]),
        PolygonObstacle([(30, 5), (31, 5), (31, 15), (30, 15)]),
        PolygonObstacle([(40, 10), (41, 10), (41, 20), (40, 20)]),
        # 水平墙壁
        PolygonObstacle([(5, 20), (15, 20), (15, 21), (5, 21)]),
        PolygonObstacle([(25, 18), (35, 18), (35, 19), (25, 19)]),
        PolygonObstacle([(15, 28), (25, 28), (25, 29), (15, 29)]),
        PolygonObstacle([(35, 38), (45, 38), (45, 39), (35, 39)]),
    ]

    for wall in walls:
        planner.add_obstacle(wall)


def test_complex_scenario():
    """测试复杂场景"""
    print("创建复杂测试场景...")

    # 创建规划器，使用较小的安全边距以适应密集环境
    planner = VoronoiPlanner(map_size=(50, 50), safety_margin=0.8)

    print("添加复杂多边形障碍物...")
    create_complex_polygon_obstacles(planner)

    print("添加圆形障碍物...")
    create_circle_obstacles(planner)

    print("添加迷宫式障碍物...")
    create_maze_like_obstacles(planner)

    print("添加大量随机点障碍物...")
    create_random_point_obstacles(planner, num_points=25)

    print(f"总共添加了 {len(planner.obstacles)} 个障碍物")

    # 生成Voronoi图
    print("生成Voronoi图...")
    success = planner.generate_voronoi_diagram()

    if not success:
        print("Voronoi图生成失败！")
        return False

    print("Voronoi图生成成功！")
    print(f"Voronoi顶点数量: {len(planner.voronoi.vertices)}")
    print(f"有效图节点数量: {len(planner.voronoi_graph)}")

    # 测试多个路径规划场景
    test_cases = [
        ((3, 3), (47, 47), "对角线路径"),
        ((5, 25), (45, 25), "水平路径"),
        ((25, 5), (25, 45), "垂直路径"),
        ((5, 45), (45, 5), "反对角线路径"),
        ((10, 10), (40, 40), "中等距离路径"),
    ]

    successful_paths = 0
    all_paths = []

    for start, goal, description in test_cases:
        print(f"\n测试 {description}: {start} -> {goal}")

        # 使用带重试机制的路径规划
        path = planner.find_path_with_retry(start, goal, max_retries=3)

        if path:
            print(f"✅ 路径规划成功！路径包含 {len(path)} 个点")

            # 获取路径统计信息
            stats = planner.get_path_statistics(path)
            print(f"   总距离: {stats['total_distance']:.2f}")
            print(f"   平均段长: {stats['average_segment_length']:.2f}")

            # 验证路径
            if planner.validate_path(path):
                print("   ✅ 路径验证通过")
                successful_paths += 1
                all_paths.append((path, description, start, goal))
            else:
                print("   ❌ 路径验证失败")
        else:
            print("❌ 路径规划失败")

    print(
        f"\n路径规划成功率: {successful_paths}/{len(test_cases)} ({successful_paths / len(test_cases) * 100:.1f}%)"
    )

    # 可视化最成功的路径
    if all_paths:
        print("创建可视化...")
        visualizer = VoronoiVisualizer(planner, figsize=(15, 12))

        # 显示第一个成功的路径
        path, description, start, goal = all_paths[0]
        visualizer.show_complete_visualization(start, goal, path)

        # 保存结果
        filename = "complex_scenario_result.png"
        visualizer.save_figure(filename)
        print(f"结果已保存为: {filename}")

        # 显示图形
        visualizer.show()

        return True

    return False


if __name__ == "__main__":
    print("复杂场景Voronoi路径规划测试")
    print("=" * 50)

    success = test_complex_scenario()

    if success:
        print("\n🎉 复杂场景测试完成！")
    else:
        print("\n❌ 复杂场景测试失败！")
