"""
基于Voronoi图的路径规划算法
支持多种障碍物类型：多边形、圆形、随机点
具有良好的鲁棒性和可视化功能
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial import Voronoi, voronoi_plot_2d
from scipy.spatial.distance import euclidean
import heapq
from typing import List, Tuple, Optional, Union
import math


class VoronoiPlanner:
    """基于Voronoi图的路径规划器"""
    
    def __init__(self, map_size: Tuple[float, float] = (50.0, 50.0), 
                 safety_margin: float = 1.0):
        """
        初始化Voronoi路径规划器
        
        Args:
            map_size: 地图尺寸 (width, height)
            safety_margin: 安全边距，避免路径过于接近障碍物
        """
        self.map_width, self.map_height = map_size
        self.safety_margin = safety_margin
        self.obstacles = []
        self.voronoi = None
        self.voronoi_points = []
        self.voronoi_graph = {}
        
    def add_obstacle(self, obstacle):
        """添加障碍物"""
        self.obstacles.append(obstacle)
        
    def clear_obstacles(self):
        """清除所有障碍物"""
        self.obstacles.clear()
        
    def _sample_boundary_points(self, num_boundary_points: int = 100) -> List[Tuple[float, float]]:
        """采样地图边界点"""
        boundary_points = []
        
        # 上边界
        for i in range(num_boundary_points // 4):
            x = i * self.map_width / (num_boundary_points // 4)
            boundary_points.append((x, 0))
            
        # 右边界
        for i in range(num_boundary_points // 4):
            y = i * self.map_height / (num_boundary_points // 4)
            boundary_points.append((self.map_width, y))
            
        # 下边界
        for i in range(num_boundary_points // 4):
            x = self.map_width - i * self.map_width / (num_boundary_points // 4)
            boundary_points.append((x, self.map_height))
            
        # 左边界
        for i in range(num_boundary_points // 4):
            y = self.map_height - i * self.map_height / (num_boundary_points // 4)
            boundary_points.append((0, y))
            
        return boundary_points
        
    def _collect_obstacle_points(self) -> List[Tuple[float, float]]:
        """收集所有障碍物的边界点"""
        points = []
        for obstacle in self.obstacles:
            points.extend(obstacle.get_boundary_points())
        return points
        
    def generate_voronoi_diagram(self):
        """生成Voronoi图"""
        # 收集所有点：障碍物边界点 + 地图边界点
        obstacle_points = self._collect_obstacle_points()
        boundary_points = self._sample_boundary_points()
        
        if not obstacle_points:
            print("警告：没有障碍物，无法生成有效的Voronoi图")
            return False
            
        all_points = obstacle_points + boundary_points
        self.voronoi_points = np.array(all_points)
        
        try:
            # 生成Voronoi图
            self.voronoi = Voronoi(self.voronoi_points)
            self._build_voronoi_graph()
            return True
        except Exception as e:
            print(f"生成Voronoi图时出错: {e}")
            return False
            
    def _build_voronoi_graph(self):
        """构建Voronoi图的图结构用于路径搜索"""
        self.voronoi_graph = {}
        
        if self.voronoi is None:
            return
            
        # 获取有限的Voronoi边
        for ridge_vertices in self.voronoi.ridge_vertices:
            if -1 not in ridge_vertices:  # 排除无限边
                v1, v2 = ridge_vertices
                vertex1 = tuple(self.voronoi.vertices[v1])
                vertex2 = tuple(self.voronoi.vertices[v2])
                
                # 检查边是否在地图范围内且不与障碍物碰撞
                if (self._is_point_valid(vertex1) and self._is_point_valid(vertex2) and
                    self._is_edge_valid(vertex1, vertex2)):
                    
                    # 添加双向边
                    if vertex1 not in self.voronoi_graph:
                        self.voronoi_graph[vertex1] = []
                    if vertex2 not in self.voronoi_graph:
                        self.voronoi_graph[vertex2] = []
                        
                    self.voronoi_graph[vertex1].append(vertex2)
                    self.voronoi_graph[vertex2].append(vertex1)
                    
    def _is_point_valid(self, point: Tuple[float, float]) -> bool:
        """检查点是否在地图范围内且不与障碍物碰撞"""
        x, y = point
        
        # 检查是否在地图范围内
        if x < 0 or x > self.map_width or y < 0 or y > self.map_height:
            return False
            
        # 检查是否与障碍物碰撞（考虑安全边距）
        for obstacle in self.obstacles:
            if obstacle.is_point_inside(point, margin=self.safety_margin):
                return False
                
        return True

    def find_path(self, start: Tuple[float, float], goal: Tuple[float, float]) -> Optional[List[Tuple[float, float]]]:
        """
        使用A*算法在Voronoi图上寻找路径

        Args:
            start: 起始点
            goal: 目标点

        Returns:
            路径点列表，如果找不到路径则返回None
        """
        if self.voronoi is None:
            print("错误：请先生成Voronoi图")
            return None

        # 找到起始点和目标点在Voronoi图中的最近节点
        start_node = self._find_nearest_valid_node(start)
        goal_node = self._find_nearest_valid_node(goal)

        if start_node is None or goal_node is None:
            print("错误：无法找到有效的起始或目标节点")
            return None

        # 使用A*算法搜索路径
        path = self._a_star_search(start_node, goal_node)

        if path is None:
            return None

        # 添加实际的起始点和目标点
        full_path = [start] + path + [goal]

        # 路径平滑
        smoothed_path = self._smooth_path(full_path)

        return smoothed_path

    def _find_nearest_valid_node(self, point: Tuple[float, float]) -> Optional[Tuple[float, float]]:
        """找到距离给定点最近的有效Voronoi节点"""
        if not self.voronoi_graph:
            return None

        min_distance = float('inf')
        nearest_node = None

        for node in self.voronoi_graph.keys():
            # 检查从给定点到节点的连线是否有效
            if self._is_edge_valid(point, node):
                distance = euclidean(point, node)
                if distance < min_distance:
                    min_distance = distance
                    nearest_node = node

        return nearest_node

    def _a_star_search(self, start: Tuple[float, float], goal: Tuple[float, float]) -> Optional[List[Tuple[float, float]]]:
        """A*算法搜索路径"""
        open_set = [(0, start)]
        came_from = {}
        g_score = {start: 0}
        f_score = {start: euclidean(start, goal)}

        while open_set:
            current = heapq.heappop(open_set)[1]

            if current == goal:
                # 重构路径
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                path.append(start)
                return path[::-1]

            if current not in self.voronoi_graph:
                continue

            for neighbor in self.voronoi_graph[current]:
                tentative_g_score = g_score[current] + euclidean(current, neighbor)

                if neighbor not in g_score or tentative_g_score < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g_score
                    f_score[neighbor] = tentative_g_score + euclidean(neighbor, goal)
                    heapq.heappush(open_set, (f_score[neighbor], neighbor))

        return None

    def _smooth_path(self, path: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
        """路径平滑：移除不必要的中间点"""
        if len(path) <= 2:
            return path

        smoothed = [path[0]]
        i = 0

        while i < len(path) - 1:
            j = len(path) - 1
            # 从最远的点开始，找到可以直接连接的点
            while j > i + 1:
                if self._is_edge_valid(path[i], path[j]):
                    smoothed.append(path[j])
                    i = j
                    break
                j -= 1
            else:
                # 如果没找到可以直接连接的点，添加下一个点
                smoothed.append(path[i + 1])
                i += 1

        return smoothed

    def validate_path(self, path: List[Tuple[float, float]]) -> bool:
        """验证路径的有效性"""
        if not path or len(path) < 2:
            return False

        # 检查每个路径点是否有效
        for point in path:
            if not self._is_point_valid(point):
                return False

        # 检查每条路径段是否有效
        for i in range(len(path) - 1):
            if not self._is_edge_valid(path[i], path[i + 1]):
                return False

        return True

    def get_path_statistics(self, path: List[Tuple[float, float]]) -> dict:
        """获取路径统计信息"""
        if not path:
            return {}

        total_distance = 0
        max_segment_length = 0
        min_segment_length = float('inf')

        for i in range(len(path) - 1):
            segment_length = euclidean(path[i], path[i + 1])
            total_distance += segment_length
            max_segment_length = max(max_segment_length, segment_length)
            min_segment_length = min(min_segment_length, segment_length)

        return {
            'total_distance': total_distance,
            'num_waypoints': len(path),
            'max_segment_length': max_segment_length,
            'min_segment_length': min_segment_length if min_segment_length != float('inf') else 0,
            'average_segment_length': total_distance / (len(path) - 1) if len(path) > 1 else 0
        }

    def adaptive_safety_margin(self, base_margin: float = None) -> float:
        """根据障碍物密度自适应调整安全边距"""
        if base_margin is None:
            base_margin = self.safety_margin

        if not self.obstacles:
            return base_margin

        # 计算障碍物密度
        total_obstacle_area = 0
        map_area = self.map_width * self.map_height

        for obstacle in self.obstacles:
            if isinstance(obstacle, CircleObstacle):
                total_obstacle_area += math.pi * obstacle.radius ** 2
            elif isinstance(obstacle, PolygonObstacle):
                # 简化计算：使用边界框面积
                vertices = obstacle.vertices
                if vertices:
                    min_x = min(v[0] for v in vertices)
                    max_x = max(v[0] for v in vertices)
                    min_y = min(v[1] for v in vertices)
                    max_y = max(v[1] for v in vertices)
                    total_obstacle_area += (max_x - min_x) * (max_y - min_y)
            elif isinstance(obstacle, PointObstacle):
                total_obstacle_area += math.pi * obstacle.influence_radius ** 2

        density = total_obstacle_area / map_area

        # 根据密度调整安全边距
        if density > 0.3:  # 高密度
            return base_margin * 1.5
        elif density > 0.15:  # 中密度
            return base_margin * 1.2
        else:  # 低密度
            return base_margin

    def find_path_with_retry(self, start: Tuple[float, float], goal: Tuple[float, float],
                           max_retries: int = 3) -> Optional[List[Tuple[float, float]]]:
        """带重试机制的路径规划"""
        original_margin = self.safety_margin

        for attempt in range(max_retries):
            try:
                # 第一次尝试使用原始安全边距
                if attempt == 0:
                    self.safety_margin = original_margin
                else:
                    # 后续尝试逐渐减小安全边距
                    self.safety_margin = original_margin * (0.8 ** attempt)
                    print(f"重试 {attempt}: 调整安全边距为 {self.safety_margin:.2f}")

                # 重新生成Voronoi图
                if not self.generate_voronoi_diagram():
                    continue

                # 尝试路径规划
                path = self.find_path(start, goal)

                if path and self.validate_path(path):
                    self.safety_margin = original_margin  # 恢复原始安全边距
                    return path

            except Exception as e:
                print(f"路径规划尝试 {attempt + 1} 失败: {e}")

        self.safety_margin = original_margin  # 恢复原始安全边距
        return None
        
    def _is_edge_valid(self, point1: Tuple[float, float], point2: Tuple[float, float]) -> bool:
        """检查边是否与障碍物碰撞"""
        # 简单的线段碰撞检测：在线段上采样多个点进行检查
        num_samples = int(euclidean(point1, point2) * 2)  # 根据距离确定采样数量
        num_samples = max(10, min(num_samples, 50))  # 限制采样数量
        
        for i in range(num_samples + 1):
            t = i / num_samples
            x = point1[0] + t * (point2[0] - point1[0])
            y = point1[1] + t * (point2[1] - point1[1])
            
            if not self._is_point_valid((x, y)):
                return False
                
        return True
