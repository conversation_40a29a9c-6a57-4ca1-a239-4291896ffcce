"""
基本功能测试脚本
验证Voronoi路径规划算法的核心功能
"""

from voronoi_planner import VoronoiPlanner
from obstacles import PolygonObstacle, CircleObstacle, PointObstacle
from visualizer import VoronoiVisualizer


def test_basic_functionality():
    """测试基本功能"""
    print("测试基本功能...")
    
    # 创建简单的测试场景
    planner = VoronoiPlanner(map_size=(50, 50), safety_margin=1.0)
    
    # 添加一个简单的矩形障碍物
    rect = PolygonObstacle([
        (20, 20), (30, 20), (30, 30), (20, 30)
    ])
    planner.add_obstacle(rect)
    
    # 添加一个圆形障碍物
    circle = CircleObstacle(center=(35, 15), radius=4)
    planner.add_obstacle(circle)
    
    print(f"添加了 {len(planner.obstacles)} 个障碍物")
    
    # 生成Voronoi图
    print("生成Voronoi图...")
    success = planner.generate_voronoi_diagram()
    
    if success:
        print("Voronoi图生成成功！")
        print(f"Voronoi顶点数量: {len(planner.voronoi.vertices)}")
        print(f"图节点数量: {len(planner.voronoi_graph)}")
    else:
        print("Voronoi图生成失败！")
        return False
    
    # 测试路径规划
    start = (5, 5)
    goal = (45, 45)
    
    print(f"规划从 {start} 到 {goal} 的路径...")
    path = planner.find_path(start, goal)
    
    if path:
        print(f"路径规划成功！路径包含 {len(path)} 个点")
        print("路径点:")
        for i, point in enumerate(path):
            print(f"  {i}: ({point[0]:.2f}, {point[1]:.2f})")
    else:
        print("路径规划失败！")
        return False
    
    # 可视化
    print("创建可视化...")
    visualizer = VoronoiVisualizer(planner)
    visualizer.show_complete_visualization(start, goal, path)
    visualizer.save_figure("test_basic_result.png")
    visualizer.show()
    
    return True


if __name__ == "__main__":
    print("开始基本功能测试")
    print("=" * 30)
    
    success = test_basic_functionality()
    
    if success:
        print("\n✅ 所有测试通过！")
    else:
        print("\n❌ 测试失败！")
