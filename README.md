# 基于Voronoi图的路径规划算法

这是一个鲁棒性强的基于Voronoi图的路径规划算法实现，支持多种类型的障碍物，并提供了完整的可视化功能。

## 特性

- **多种障碍物支持**：多边形、圆形、点障碍物
- **鲁棒性优化**：自适应安全边距、路径验证、重试机制
- **高效路径搜索**：基于A*算法的Voronoi图路径搜索
- **路径平滑**：自动移除不必要的中间点
- **完整可视化**：实时显示Voronoi图、障碍物和规划路径
- **性能统计**：提供详细的路径和性能统计信息

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 基本使用

```python
from voronoi_planner import VoronoiPlanner
from obstacles import PolygonObstacle, CircleObstacle
from visualizer import VoronoiVisualizer

# 创建规划器
planner = VoronoiPlanner(map_size=(50, 50), safety_margin=1.0)

# 添加障碍物
rect = PolygonObstacle([(10, 10), (20, 10), (20, 20), (10, 20)])
circle = CircleObstacle(center=(30, 30), radius=5)
planner.add_obstacle(rect)
planner.add_obstacle(circle)

# 生成Voronoi图
planner.generate_voronoi_diagram()

# 路径规划
start = (5, 5)
goal = (45, 45)
path = planner.find_path(start, goal)

# 可视化
visualizer = VoronoiVisualizer(planner)
visualizer.show_complete_visualization(start, goal, path)
visualizer.show()
```

### 运行演示

```bash
# 基本功能测试
python test_basic.py

# 完整演示（包含3个不同场景）
python demo.py
```

## 核心组件

### VoronoiPlanner
主要的路径规划类，包含以下核心方法：
- `add_obstacle(obstacle)`: 添加障碍物
- `generate_voronoi_diagram()`: 生成Voronoi图
- `find_path(start, goal)`: 路径规划
- `find_path_with_retry(start, goal)`: 带重试机制的路径规划

### 障碍物类型

#### PolygonObstacle
多边形障碍物，支持任意凸多边形和简单凹多边形。

```python
# 矩形
rect = PolygonObstacle([(0, 0), (10, 0), (10, 5), (0, 5)])

# 三角形
triangle = PolygonObstacle([(0, 0), (10, 0), (5, 10)])
```

#### CircleObstacle
圆形障碍物。

```python
circle = CircleObstacle(center=(25, 25), radius=8)
```

#### PointObstacle
点障碍物，具有影响半径。

```python
point = PointObstacle(position=(15, 15), influence_radius=2)
```

### VoronoiVisualizer
可视化组件，提供丰富的绘图功能：
- 障碍物可视化
- Voronoi图显示
- 路径可视化
- 安全边距显示

## 算法原理

### Voronoi图生成
1. 收集所有障碍物的边界点
2. 添加地图边界点
3. 使用scipy.spatial.Voronoi生成Voronoi图
4. 过滤无效边和顶点

### 路径搜索
1. 找到起始点和目标点的最近有效Voronoi节点
2. 使用A*算法在Voronoi图上搜索路径
3. 路径平滑优化
4. 路径验证

### 鲁棒性优化
- **自适应安全边距**：根据障碍物密度调整
- **路径验证**：确保路径不与障碍物碰撞
- **重试机制**：失败时自动调整参数重试
- **边界处理**：正确处理地图边界

## 性能特点

- **时间复杂度**：O(n log n) for Voronoi图生成，O(E log V) for A*搜索
- **空间复杂度**：O(n) for 存储Voronoi图结构
- **适用场景**：中等规模环境（推荐50x50到200x200）

## 演示场景

### 场景1：基本几何障碍物
包含矩形、三角形和圆形障碍物的基本测试场景。

### 场景2：复杂障碍物组合
包含L形多边形、星形障碍物和随机点障碍物。

### 场景3：迷宫式环境
模拟复杂的迷宫环境，测试算法在密集障碍物中的表现。

## 扩展建议

1. **动态障碍物**：添加移动障碍物支持
2. **多机器人**：扩展为多机器人路径规划
3. **实时重规划**：支持环境变化时的实时路径更新
4. **3D扩展**：扩展到三维空间
5. **优化算法**：集成RRT*、PRM等其他算法

## 注意事项

1. 确保障碍物顶点按正确顺序排列（多边形）
2. 避免障碍物过于密集，可能导致无解
3. 合理设置安全边距，平衡安全性和路径质量
4. 大规模环境建议增加采样点数量
